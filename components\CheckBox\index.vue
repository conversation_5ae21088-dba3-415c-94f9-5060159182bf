<template>
  <view class="checkbox-wrapper">
    <!-- 单个复选框模式 -->
    <view
      v-if="!isInGroup"
      class="checkbox-item"
      :class="{ 'checkbox-disabled': disabled }"
      @click="handleClick"
    >
      <view class="checkbox-box" :class="checkboxClass">
        <text v-if="currentChecked" class="checkbox-icon">✓</text>
      </view>
      <text v-if="label" class="checkbox-label">{{ label }}</text>
      <slot></slot>
    </view>

    <!-- 复选框组模式 -->
    <view v-else class="checkbox-group">
      <view
        v-for="(option, index) in options"
        :key="option.value || index"
        class="checkbox-item"
        :class="{ 'checkbox-disabled': disabled || option.disabled }"
        @click="handleGroupClick(option, index)"
      >
        <view class="checkbox-box" :class="getGroupCheckboxClass(option)">
          <text v-if="isOptionChecked(option)" class="checkbox-icon">✓</text>
        </view>
        <text class="checkbox-label">{{ option.label || option.text || option.name }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CheckBox',
  props: {
    // 基础属性
    value: {
      type: [Boolean, Array],
      default: false
    },
    label: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },

    // 复选框组属性
    options: {
      type: Array,
      default: () => []
    },
    max: {
      type: Number,
      default: Infinity
    },
    min: {
      type: Number,
      default: 0
    },

    // 样式属性
    color: {
      type: String,
      default: '#007aff'
    },
    size: {
      type: String,
      default: 'medium' // small, medium, large
    },
    shape: {
      type: String,
      default: 'square' // square, round
    }
  },

  data() {
    return {
      currentValue: this.value
    }
  },

  computed: {
    // 判断是否在组模式下
    isInGroup() {
      return this.options && this.options.length > 0
    },

    // 单个复选框的选中状态
    currentChecked() {
      return this.isInGroup ? false : this.currentValue
    },

    // 复选框样式类
    checkboxClass() {
      return {
        'checkbox-checked': this.currentChecked,
        'checkbox-disabled': this.disabled,
        [`checkbox-${this.size}`]: true,
        [`checkbox-${this.shape}`]: true
      }
    },

    // 当前选中的值数组（组模式）
    selectedValues() {
      if (!this.isInGroup) return []
      return Array.isArray(this.currentValue) ? this.currentValue : []
    }
  },

  watch: {
    value: {
      handler(newVal) {
        this.currentValue = newVal
      },
      immediate: true
    }
  },

  methods: {
    // 单个复选框点击处理
    handleClick() {
      if (this.disabled) return

      const newValue = !this.currentChecked
      this.currentValue = newValue
      this.$emit('input', newValue)
      this.$emit('change', newValue)
    },

    // 复选框组点击处理
    handleGroupClick(option, index) {
      if (this.disabled || option.disabled) return

      const optionValue = option.value !== undefined ? option.value : index
      let newSelectedValues = [...this.selectedValues]

      const currentIndex = newSelectedValues.indexOf(optionValue)

      if (currentIndex > -1) {
        // 取消选中
        if (newSelectedValues.length > this.min) {
          newSelectedValues.splice(currentIndex, 1)
        }
      } else {
        // 选中
        if (newSelectedValues.length < this.max) {
          newSelectedValues.push(optionValue)
        }
      }

      this.currentValue = newSelectedValues
      this.$emit('input', newSelectedValues)
      this.$emit('change', newSelectedValues, option)
    },

    // 判断选项是否被选中
    isOptionChecked(option) {
      const optionValue = option.value !== undefined ? option.value : option
      return this.selectedValues.includes(optionValue)
    },

    // 获取组模式下复选框的样式类
    getGroupCheckboxClass(option) {
      return {
        'checkbox-checked': this.isOptionChecked(option),
        'checkbox-disabled': this.disabled || option.disabled,
        [`checkbox-${this.size}`]: true,
        [`checkbox-${this.shape}`]: true
      }
    },

    // 全选
    selectAll() {
      if (!this.isInGroup || this.disabled) return

      const allValues = this.options
        .filter(option => !option.disabled)
        .map((option, index) => option.value !== undefined ? option.value : index)
        .slice(0, this.max)

      this.currentValue = allValues
      this.$emit('input', allValues)
      this.$emit('change', allValues)
    },

    // 取消全选
    clearAll() {
      if (!this.isInGroup || this.disabled) return

      this.currentValue = []
      this.$emit('input', [])
      this.$emit('change', [])
    },

    // 反选
    toggleAll() {
      if (!this.isInGroup || this.disabled) return

      const allValues = this.options
        .filter(option => !option.disabled)
        .map((option, index) => option.value !== undefined ? option.value : index)

      const unselectedValues = allValues.filter(value => !this.selectedValues.includes(value))
      const newSelectedValues = unselectedValues.slice(0, this.max)

      this.currentValue = newSelectedValues
      this.$emit('input', newSelectedValues)
      this.$emit('change', newSelectedValues)
    }
  }
}
</script>

<style scoped>
.checkbox-wrapper {
  width: 100%;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 8rpx 0;
  cursor: pointer;
}

.checkbox-item.checkbox-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checkbox-box {
  position: relative;
  border: 2rpx solid #ddd;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: all 0.3s ease;
}

/* 尺寸样式 */
.checkbox-small {
  width: 32rpx;
  height: 32rpx;
}

.checkbox-medium {
  width: 40rpx;
  height: 40rpx;
}

.checkbox-large {
  width: 48rpx;
  height: 48rpx;
}

/* 形状样式 */
.checkbox-square {
  border-radius: 6rpx;
}

.checkbox-round {
  border-radius: 50%;
}

/* 选中状态 */
.checkbox-checked {
  border-color: #007aff;
  background-color: #007aff;
}

.checkbox-disabled.checkbox-checked {
  border-color: #ccc;
  background-color: #ccc;
}

.checkbox-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  line-height: 1;
}

.checkbox-label {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.checkbox-disabled .checkbox-label {
  color: #999;
}

/* 悬停效果（仅H5） */
/* #ifdef H5 */
.checkbox-item:not(.checkbox-disabled):hover .checkbox-box {
  border-color: #007aff;
}
/* #endif */

/* 焦点效果 */
.checkbox-box:focus {
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.2);
}
</style>