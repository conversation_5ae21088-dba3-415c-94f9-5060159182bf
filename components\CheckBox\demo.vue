<template>
  <view class="demo-container">
    <view class="demo-section">
      <text class="demo-title">单个复选框示例</text>
      
      <!-- 基础复选框 -->
      <view class="demo-item">
        <text class="demo-label">基础复选框：</text>
        <CheckBox v-model="singleValue" label="同意用户协议" @change="onSingleChange" />
      </view>
      
      <!-- 禁用状态 -->
      <view class="demo-item">
        <text class="demo-label">禁用状态：</text>
        <CheckBox v-model="disabledValue" label="已禁用" disabled />
      </view>
      
      <!-- 不同尺寸 -->
      <view class="demo-item">
        <text class="demo-label">不同尺寸：</text>
        <CheckBox v-model="smallValue" label="小尺寸" size="small" />
        <CheckBox v-model="mediumValue" label="中尺寸" size="medium" />
        <CheckBox v-model="largeValue" label="大尺寸" size="large" />
      </view>
      
      <!-- 不同形状 -->
      <view class="demo-item">
        <text class="demo-label">不同形状：</text>
        <CheckBox v-model="squareValue" label="方形" shape="square" />
        <CheckBox v-model="roundValue" label="圆形" shape="round" />
      </view>
    </view>
    
    <view class="demo-section">
      <text class="demo-title">复选框组示例</text>
      
      <!-- 基础复选框组 -->
      <view class="demo-item">
        <text class="demo-label">兴趣爱好：</text>
        <CheckBox 
          v-model="hobbies" 
          :options="hobbyOptions" 
          @change="onGroupChange"
        />
        <text class="demo-result">已选择：{{ hobbies.join(', ') }}</text>
      </view>
      
      <!-- 限制选择数量 -->
      <view class="demo-item">
        <text class="demo-label">技能选择（最多选3个）：</text>
        <CheckBox 
          v-model="skills" 
          :options="skillOptions" 
          :max="3"
          @change="onSkillChange"
        />
        <text class="demo-result">已选择：{{ skills.length }}/3</text>
      </view>
      
      <!-- 带禁用选项的组 -->
      <view class="demo-item">
        <text class="demo-label">城市选择：</text>
        <CheckBox 
          v-model="cities" 
          :options="cityOptions" 
          @change="onCityChange"
        />
      </view>
      
      <!-- 操作按钮 -->
      <view class="demo-buttons">
        <button @click="selectAllHobbies" class="demo-btn">全选兴趣</button>
        <button @click="clearAllHobbies" class="demo-btn">清空兴趣</button>
        <button @click="toggleAllHobbies" class="demo-btn">反选兴趣</button>
      </view>
    </view>
  </view>
</template>

<script>
import CheckBox from './index.vue'

export default {
  name: 'CheckBoxDemo',
  components: {
    CheckBox
  },
  
  data() {
    return {
      // 单个复选框数据
      singleValue: false,
      disabledValue: true,
      smallValue: false,
      mediumValue: true,
      largeValue: false,
      squareValue: false,
      roundValue: true,
      
      // 复选框组数据
      hobbies: ['reading', 'music'],
      skills: [],
      cities: ['beijing'],
      
      // 选项数据
      hobbyOptions: [
        { label: '阅读', value: 'reading' },
        { label: '音乐', value: 'music' },
        { label: '运动', value: 'sports' },
        { label: '旅行', value: 'travel' },
        { label: '摄影', value: 'photography' }
      ],
      
      skillOptions: [
        { label: 'JavaScript', value: 'js' },
        { label: 'Vue.js', value: 'vue' },
        { label: 'React', value: 'react' },
        { label: 'Node.js', value: 'node' },
        { label: 'Python', value: 'python' },
        { label: 'Java', value: 'java' }
      ],
      
      cityOptions: [
        { label: '北京', value: 'beijing' },
        { label: '上海', value: 'shanghai' },
        { label: '广州', value: 'guangzhou' },
        { label: '深圳', value: 'shenzhen', disabled: true }, // 禁用选项
        { label: '杭州', value: 'hangzhou' }
      ]
    }
  },
  
  methods: {
    onSingleChange(value) {
      console.log('单个复选框变化：', value)
    },
    
    onGroupChange(values) {
      console.log('兴趣爱好变化：', values)
    },
    
    onSkillChange(values) {
      console.log('技能选择变化：', values)
      if (values.length >= 3) {
        uni.showToast({
          title: '最多只能选择3个技能',
          icon: 'none'
        })
      }
    },
    
    onCityChange(values) {
      console.log('城市选择变化：', values)
    },
    
    selectAllHobbies() {
      this.$refs.hobbyCheckbox && this.$refs.hobbyCheckbox.selectAll()
    },
    
    clearAllHobbies() {
      this.$refs.hobbyCheckbox && this.$refs.hobbyCheckbox.clearAll()
    },
    
    toggleAllHobbies() {
      this.$refs.hobbyCheckbox && this.$refs.hobbyCheckbox.toggleAll()
    }
  }
}
</script>

<style scoped>
.demo-container {
  padding: 40rpx;
}

.demo-section {
  margin-bottom: 60rpx;
}

.demo-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.demo-item {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.demo-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.demo-result {
  font-size: 24rpx;
  color: #007aff;
  margin-top: 20rpx;
  display: block;
}

.demo-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.demo-btn {
  padding: 20rpx 30rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  border: 1rpx solid #007aff;
  background-color: #fff;
  color: #007aff;
}

.demo-btn:active {
  background-color: #007aff;
  color: #fff;
}
</style>
